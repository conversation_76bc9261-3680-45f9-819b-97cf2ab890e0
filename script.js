// Custom Cursor Implementation
class CustomCursor {
    constructor() {
        this.cursor = document.getElementById('customCursor');
        this.isVisible = false;
        this.isHovering = false;
        this.isClicking = false;
        this.isTextMode = false;

        this.init();
    }

    init() {
        // Mouse move handler
        document.addEventListener('mousemove', (e) => {
            const magneticElement = this.findMagneticElement(e.target);
            if (magneticElement) {
                const rect = magneticElement.getBoundingClientRect();
                const centerX = rect.left + rect.width / 2;
                const centerY = rect.top + rect.height / 2;
                const distance = Math.sqrt(Math.pow(e.clientX - centerX, 2) + Math.pow(e.clientY - centerY, 2));

                if (distance < 50) {
                    // Magnetic effect - pull cursor towards center
                    const pullStrength = 0.3;
                    const pullX = centerX + (e.clientX - centerX) * (1 - pullStrength);
                    const pullY = centerY + (e.clientY - centerY) * (1 - pullStrength);
                    this.updatePosition(pullX, pullY);
                } else {
                    this.updatePosition(e.clientX, e.clientY);
                }
            } else {
                this.updatePosition(e.clientX, e.clientY);
            }
            this.show();
        });

        // Mouse leave handler
        document.addEventListener('mouseleave', () => {
            this.hide();
        });

        // Mouse enter handler
        document.addEventListener('mouseenter', () => {
            this.show();
        });

        // Click handlers
        document.addEventListener('mousedown', (e) => {
            this.setClickState(true);
            this.createRipple(e.clientX, e.clientY);
        });

        document.addEventListener('mouseup', () => {
            this.setClickState(false);
        });

        // Hover handlers for interactive elements
        this.addHoverListeners();

        // Text input handlers
        this.addTextInputListeners();
    }

    updatePosition(x, y) {
        if (this.cursor) {
            this.cursor.style.left = x + 'px';
            this.cursor.style.top = y + 'px';
        }
    }

    show() {
        if (!this.isVisible && this.cursor) {
            this.cursor.style.opacity = '1';
            this.isVisible = true;
        }
    }

    hide() {
        if (this.isVisible && this.cursor) {
            this.cursor.style.opacity = '0';
            this.isVisible = false;
        }
    }

    setHoverState(isHovering) {
        if (this.isHovering !== isHovering && this.cursor) {
            this.isHovering = isHovering;
            this.cursor.classList.toggle('hover', isHovering);
        }
    }

    setClickState(isClicking) {
        if (this.isClicking !== isClicking && this.cursor) {
            this.isClicking = isClicking;
            this.cursor.classList.toggle('click', isClicking);
        }
    }

    setTextMode(isTextMode) {
        if (this.isTextMode !== isTextMode && this.cursor) {
            this.isTextMode = isTextMode;
            this.cursor.classList.toggle('text', isTextMode);
        }
    }

    addHoverListeners() {
        const interactiveElements = 'a, button, .btn, [role="button"], .nav-link, .tab-btn, .result-item, .stat-card, .upload-area';

        document.addEventListener('mouseover', (e) => {
            if (e.target.matches(interactiveElements) || e.target.closest(interactiveElements)) {
                this.setHoverState(true);

                // Add special effects for specific elements
                if (e.target.closest('.btn-primary')) {
                    this.cursor.style.background = 'var(--primary-color)';
                } else if (e.target.closest('.btn-outline')) {
                    this.cursor.style.background = 'transparent';
                    this.cursor.style.border = '2px solid var(--accent-color)';
                } else if (e.target.closest('.stat-card')) {
                    this.cursor.style.background = 'var(--success-color)';
                } else if (e.target.closest('.upload-area')) {
                    this.cursor.style.background = 'var(--warning-color)';
                }
            }
        });

        document.addEventListener('mouseout', (e) => {
            if (e.target.matches(interactiveElements) || e.target.closest(interactiveElements)) {
                this.setHoverState(false);
                // Reset cursor style
                this.cursor.style.background = 'var(--accent-color)';
                this.cursor.style.border = 'none';
            }
        });

        // Add loading state for buttons during API calls
        document.addEventListener('click', (e) => {
            if (e.target.matches('button, .btn') || e.target.closest('button, .btn')) {
                this.setLoadingState(true);
                setTimeout(() => this.setLoadingState(false), 1000);
            }
        });
    }

    setLoadingState(isLoading) {
        if (this.cursor) {
            this.cursor.classList.toggle('loading', isLoading);
        }
    }

    createRipple(x, y) {
        // Create ripple effect
        this.cursor.classList.add('ripple');
        setTimeout(() => {
            this.cursor.classList.remove('ripple');
        }, 600);

        // Create additional ripple element for better effect
        const ripple = document.createElement('div');
        ripple.style.position = 'fixed';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';
        ripple.style.width = '4px';
        ripple.style.height = '4px';
        ripple.style.background = 'rgba(74, 144, 226, 0.4)';
        ripple.style.borderRadius = '50%';
        ripple.style.transform = 'translate(-50%, -50%)';
        ripple.style.pointerEvents = 'none';
        ripple.style.zIndex = '9998';
        ripple.style.animation = 'ripple 0.6s ease-out forwards';

        document.body.appendChild(ripple);

        setTimeout(() => {
            document.body.removeChild(ripple);
        }, 600);
    }

    findMagneticElement(element) {
        const magneticSelectors = '.btn, button, .nav-link';
        return element.closest(magneticSelectors);
    }

    addTextInputListeners() {
        const textElements = 'input[type="text"], input[type="number"], textarea, input[type="search"]';

        document.addEventListener('mouseover', (e) => {
            if (e.target.matches(textElements)) {
                this.setTextMode(true);
            }
        });

        document.addEventListener('mouseout', (e) => {
            if (e.target.matches(textElements)) {
                this.setTextMode(false);
            }
        });
    }
}

// Initialize custom cursor when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new CustomCursor();
});

// API Configuration (using config.js)
const API_BASE_URL = CONFIG?.API_BASE_URL || '/plugsec/api/v1';
let authToken = localStorage.getItem(CONFIG?.STORAGE_KEYS?.AUTH_TOKEN || 'authToken');
let apiKey = localStorage.getItem(CONFIG?.STORAGE_KEYS?.API_KEY || 'apiKey');

// DOM Elements
const elements = {
    loginBtn: document.getElementById('loginBtn'),
    logoutBtn: document.getElementById('logoutBtn'),
    userProfile: document.getElementById('userProfile'),
    userAvatar: document.getElementById('userAvatar'),
    userName: document.getElementById('userName'),
    loadingOverlay: document.getElementById('loadingOverlay'),
    toastContainer: document.getElementById('toastContainer'),
    
    // Search elements
    hashInput: document.getElementById('hashInput'),
    searchHashBtn: document.getElementById('searchHashBtn'),
    batchHashes: document.getElementById('batchHashes'),
    searchBatchBtn: document.getElementById('searchBatchBtn'),
    queryInput: document.getElementById('queryInput'),
    verdictSelect: document.getElementById('verdictSelect'),
    approvedSelect: document.getElementById('approvedSelect'),
    limitInput: document.getElementById('limitInput'),
    searchAdvancedBtn: document.getElementById('searchAdvancedBtn'),
    searchResults: document.getElementById('searchResults'),
    resultsList: document.getElementById('resultsList'),
    resultsCount: document.getElementById('resultsCount'),
    
    // Stats elements
    totalScanned: document.getElementById('totalScanned'),
    safePlugins: document.getElementById('safePlugins'),
    dangerousPlugins: document.getElementById('dangerousPlugins'),
    totalDevelopers: document.getElementById('totalDevelopers'),
    developerIdInput: document.getElementById('developerIdInput'),
    getDeveloperStatsBtn: document.getElementById('getDeveloperStatsBtn'),
    developerStats: document.getElementById('developerStats'),
    channelIdInput: document.getElementById('channelIdInput'),
    getChannelStatsBtn: document.getElementById('getChannelStatsBtn'),
    channelStats: document.getElementById('channelStats'),
    
    // Scan elements
    uploadArea: document.getElementById('uploadArea'),
    fileInput: document.getElementById('fileInput'),
    scanProgress: document.getElementById('scanProgress'),
    scanResult: document.getElementById('scanResult')
};

// Utility Functions
function showLoading() {
    elements.loadingOverlay.classList.remove('hidden');
}

function hideLoading() {
    elements.loadingOverlay.classList.add('hidden');
}

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.textContent = message;
    
    elements.toastContainer.appendChild(toast);
    
    setTimeout(() => {
        toast.style.animation = 'slideOut 0.3s ease-in forwards';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 5000);
}

function scrollToSection(sectionId) {
    document.getElementById(sectionId).scrollIntoView({ behavior: 'smooth' });
}

// API Functions
async function makeRequest(endpoint, options = {}) {
    const url = `${API_BASE_URL}${endpoint}`;
    const headers = {
        'Content-Type': 'application/json',
        ...options.headers
    };
    
    if (authToken) {
        headers['Authorization'] = `Bearer ${authToken}`;
    }
    
    if (apiKey) {
        headers['X-API-Key'] = apiKey;
    }
    
    try {
        const response = await fetch(url, {
            ...options,
            headers
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return await response.json();
    } catch (error) {
        console.error('API request failed:', error);
        throw error;
    }
}

// Authentication
function updateAuthUI() {
    if (authToken) {
        elements.loginBtn.classList.add('hidden');
        elements.userProfile.classList.remove('hidden');
        loadUserProfile();
    } else {
        elements.loginBtn.classList.remove('hidden');
        elements.userProfile.classList.add('hidden');
    }
}

async function loadUserProfile() {
    try {
        const response = await makeRequest('/users/me');
        if (response.status === 'success') {
            const userData = response.data;
            elements.userName.textContent = userData.first_name || 'Пользователь';
            if (userData.photo_url) {
                elements.userAvatar.src = userData.photo_url;
            }
        }
    } catch (error) {
        console.error('Failed to load user profile:', error);
        showToast('Ошибка загрузки профиля пользователя', 'error');
    }
}

function logout() {
    authToken = null;
    localStorage.removeItem('authToken');
    updateAuthUI();
    showToast('Вы вышли из системы', 'info');
}

// Search Functions
async function searchByHash() {
    const hash = elements.hashInput.value.trim();
    if (!hash) {
        showToast('Введите хэш файла', 'warning');
        return;
    }
    
    showLoading();
    try {
        const response = await makeRequest(`/report/${hash}`);
        displaySearchResults([response.data]);
    } catch (error) {
        showToast('Ошибка поиска по хэшу', 'error');
    } finally {
        hideLoading();
    }
}

async function searchBatch() {
    const hashesText = elements.batchHashes.value.trim();
    if (!hashesText) {
        showToast('Введите хэши файлов', 'warning');
        return;
    }
    
    const hashes = hashesText.split('\n').map(h => h.trim()).filter(h => h);
    if (hashes.length === 0) {
        showToast('Введите корректные хэши', 'warning');
        return;
    }
    
    showLoading();
    try {
        const response = await makeRequest('/reports/batch', {
            method: 'POST',
            body: JSON.stringify({ hashes })
        });
        displaySearchResults(response.data);
    } catch (error) {
        showToast('Ошибка пакетного поиска', 'error');
    } finally {
        hideLoading();
    }
}

async function searchAdvanced() {
    if (!authToken) {
        showToast('Для расширенного поиска необходима авторизация', 'warning');
        return;
    }
    
    const params = new URLSearchParams();
    
    const query = elements.queryInput.value.trim();
    if (query) params.append('query', query);
    
    const verdict = elements.verdictSelect.value;
    if (verdict) params.append('verdict', verdict);
    
    const approved = elements.approvedSelect.value;
    if (approved) params.append('approved', approved);
    
    const limit = elements.limitInput.value;
    if (limit) params.append('limit', limit);
    
    showLoading();
    try {
        const response = await makeRequest(`/reports/search?${params.toString()}`);
        displaySearchResults(response.data.results || response.data);
        elements.resultsCount.textContent = response.data.total || response.data.length;
    } catch (error) {
        showToast('Ошибка расширенного поиска', 'error');
    } finally {
        hideLoading();
    }
}

function displaySearchResults(results) {
    if (!Array.isArray(results)) {
        results = [results];
    }
    
    elements.searchResults.classList.remove('hidden');
    elements.resultsCount.textContent = results.length;
    
    elements.resultsList.innerHTML = results.map(result => `
        <div class="result-item">
            <div class="result-header">
                <div class="result-hash">${result.file_hash || 'N/A'}</div>
                <div class="verdict-badge verdict-${getVerdictClass(result.verdict)}">
                    ${result.verdict || 'Неизвестно'}
                </div>
            </div>
            <div class="result-content">
                <h4>${result.plugin_name || 'Неизвестный плагин'}</h4>
                <p><strong>Размер:</strong> ${result.file_size || 'N/A'} байт</p>
                <p><strong>Дата сканирования:</strong> ${formatDate(result.scan_date)}</p>
                ${result.description ? `<p><strong>Описание:</strong> ${result.description}</p>` : ''}
                ${result.approved !== undefined ? `<p><strong>Одобрено:</strong> ${result.approved ? 'Да' : 'Нет'}</p>` : ''}
            </div>
        </div>
    `).join('');
}

function getVerdictClass(verdict) {
    if (!verdict) return 'warning';
    const lower = verdict.toLowerCase();
    if (lower.includes('безопасно') || lower.includes('safe')) return 'safe';
    if (lower.includes('опасно') || lower.includes('danger')) return 'danger';
    return 'warning';
}

function formatDate(dateString) {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('ru-RU');
}

// Statistics Functions
async function loadGlobalStats() {
    try {
        const response = await makeRequest('/stats');
        if (response.status === 'success') {
            const stats = response.data;
            elements.totalScanned.textContent = stats.total_scanned || '0';
            elements.safePlugins.textContent = stats.safe_plugins || '0';
            elements.dangerousPlugins.textContent = stats.dangerous_plugins || '0';
            elements.totalDevelopers.textContent = stats.total_developers || '0';
        }
    } catch (error) {
        console.error('Failed to load global stats:', error);
    }
}

async function getDeveloperStats() {
    const developerId = elements.developerIdInput.value.trim();
    if (!developerId) {
        showToast('Введите ID разработчика', 'warning');
        return;
    }
    
    showLoading();
    try {
        const response = await makeRequest(`/developer/${developerId}/stats`);
        displayStatsResult(response.data, elements.developerStats);
    } catch (error) {
        showToast('Ошибка получения статистики разработчика', 'error');
    } finally {
        hideLoading();
    }
}

async function getChannelStats() {
    const channelId = elements.channelIdInput.value.trim();
    if (!channelId) {
        showToast('Введите ID канала', 'warning');
        return;
    }
    
    showLoading();
    try {
        const response = await makeRequest(`/channel/${channelId}/stats`);
        displayStatsResult(response.data, elements.channelStats);
    } catch (error) {
        showToast('Ошибка получения статистики канала', 'error');
    } finally {
        hideLoading();
    }
}

function displayStatsResult(data, container) {
    container.classList.remove('hidden');
    container.innerHTML = `
        <h4>Статистика</h4>
        <pre>${JSON.stringify(data, null, 2)}</pre>
    `;
}

// File Upload Functions
async function uploadFile(file) {
    if (!authToken) {
        showToast('Для загрузки файлов необходима авторизация', 'warning');
        return;
    }
    
    const formData = new FormData();
    formData.append('file', file);
    
    elements.scanProgress.classList.remove('hidden');
    elements.scanResult.classList.add('hidden');
    
    try {
        const response = await fetch(`${API_BASE_URL}/scan/upload`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${authToken}`
            },
            body: formData
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const result = await response.json();
        displayScanResult(result.data);
    } catch (error) {
        showToast('Ошибка загрузки файла', 'error');
    } finally {
        elements.scanProgress.classList.add('hidden');
    }
}

function displayScanResult(result) {
    elements.scanResult.classList.remove('hidden');
    elements.scanResult.innerHTML = `
        <h3>Результат сканирования</h3>
        <div class="result-item">
            <div class="result-header">
                <div class="result-hash">${result.file_hash || 'N/A'}</div>
                <div class="verdict-badge verdict-${getVerdictClass(result.verdict)}">
                    ${result.verdict || 'Обрабатывается...'}
                </div>
            </div>
            <div class="result-content">
                <p><strong>Имя файла:</strong> ${result.filename || 'N/A'}</p>
                <p><strong>Размер:</strong> ${result.file_size || 'N/A'} байт</p>
                <p><strong>Статус:</strong> ${result.status || 'В обработке'}</p>
                ${result.description ? `<p><strong>Описание:</strong> ${result.description}</p>` : ''}
            </div>
        </div>
    `;
}

// Tab Management
function initTabs() {
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabPanes = document.querySelectorAll('.tab-pane');
    
    tabBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            const tabId = btn.dataset.tab;
            
            // Update active tab button
            tabBtns.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
            
            // Update active tab pane
            tabPanes.forEach(pane => pane.classList.remove('active'));
            document.getElementById(`${tabId}-tab`).classList.add('active');
        });
    });
}

// Navigation
function initNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const targetId = link.getAttribute('href').substring(1);
            scrollToSection(targetId);
            
            // Update active nav link
            navLinks.forEach(l => l.classList.remove('active'));
            link.classList.add('active');
        });
    });
    
    // Update active nav link on scroll
    window.addEventListener('scroll', () => {
        const sections = document.querySelectorAll('section[id]');
        const scrollPos = window.scrollY + 100;
        
        sections.forEach(section => {
            const top = section.offsetTop;
            const bottom = top + section.offsetHeight;
            const id = section.getAttribute('id');
            
            if (scrollPos >= top && scrollPos <= bottom) {
                navLinks.forEach(l => l.classList.remove('active'));
                const activeLink = document.querySelector(`.nav-link[href="#${id}"]`);
                if (activeLink) activeLink.classList.add('active');
            }
        });
    });
}

// File Upload Drag & Drop
function initFileUpload() {
    const uploadArea = elements.uploadArea;
    const fileInput = elements.fileInput;
    
    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });
    
    uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('dragover');
    });
    
    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            uploadFile(files[0]);
        }
    });
    
    fileInput.addEventListener('change', (e) => {
        if (e.target.files.length > 0) {
            uploadFile(e.target.files[0]);
        }
    });
}

// Event Listeners
function initEventListeners() {
    // Auth events
    elements.logoutBtn?.addEventListener('click', logout);
    
    // Search events
    elements.searchHashBtn?.addEventListener('click', searchByHash);
    elements.searchBatchBtn?.addEventListener('click', searchBatch);
    elements.searchAdvancedBtn?.addEventListener('click', searchAdvanced);
    
    // Stats events
    elements.getDeveloperStatsBtn?.addEventListener('click', getDeveloperStats);
    elements.getChannelStatsBtn?.addEventListener('click', getChannelStats);
    
    // Enter key support for inputs
    elements.hashInput?.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') searchByHash();
    });
    
    elements.developerIdInput?.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') getDeveloperStats();
    });
    
    elements.channelIdInput?.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') getChannelStats();
    });
}

// Initialize Application
function init() {
    updateAuthUI();
    initTabs();
    initNavigation();
    initFileUpload();
    initEventListeners();
    loadGlobalStats();
}

// Start the application when DOM is loaded
document.addEventListener('DOMContentLoaded', init);

// Additional Features
function setButtonLoading(button, loading = true) {
    if (loading) {
        button.disabled = true;
        button.classList.add('loading');
    } else {
        button.disabled = false;
        button.classList.remove('loading');
    }
}

// Enhanced Search with Debouncing
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Auto-complete for search
const debouncedSearch = debounce(async (query) => {
    if (query.length > 2 && authToken) {
        try {
            const response = await makeRequest(`/reports/search?query=${encodeURIComponent(query)}&limit=5`);
            // Could implement dropdown suggestions here
        } catch (error) {
            console.error('Auto-complete search failed:', error);
        }
    }
}, 300);

// Enhanced File Validation
function validateFile(file) {
    const maxSize = 50 * 1024 * 1024; // 50MB
    const allowedTypes = [
        'application/java-archive',
        'application/zip',
        'text/x-python',
        'application/javascript',
        'text/plain'
    ];

    if (file.size > maxSize) {
        showToast('Файл слишком большой. Максимальный размер: 50MB', 'error');
        return false;
    }

    // Basic file type validation (can be bypassed, but good for UX)
    const extension = file.name.split('.').pop().toLowerCase();
    const validExtensions = ['jar', 'zip', 'py', 'js', 'txt', 'java'];

    if (!validExtensions.includes(extension)) {
        showToast('Неподдерживаемый тип файла', 'warning');
        return false;
    }

    return true;
}

// Progress Animation for File Upload
function animateProgress(duration = 3000) {
    const progressFill = document.querySelector('.progress-fill');
    if (!progressFill) return;

    let start = 0;
    const increment = 100 / (duration / 50);

    const timer = setInterval(() => {
        start += increment;
        if (start >= 100) {
            start = 100;
            clearInterval(timer);
        }
        progressFill.style.width = `${start}%`;
    }, 50);

    return timer;
}

// Enhanced Upload Function
async function uploadFileEnhanced(file) {
    if (!authToken) {
        showToast('Для загрузки файлов необходима авторизация', 'warning');
        return;
    }

    if (!validateFile(file)) {
        return;
    }

    const formData = new FormData();
    formData.append('file', file);

    elements.scanProgress.classList.remove('hidden');
    elements.scanResult.classList.add('hidden');

    const progressTimer = animateProgress();

    try {
        const response = await fetch(`${API_BASE_URL}/scan/upload`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${authToken}`
            },
            body: formData
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        displayScanResult(result.data);
        showToast('Файл успешно загружен и обрабатывается', 'success');
    } catch (error) {
        showToast('Ошибка загрузки файла: ' + error.message, 'error');
    } finally {
        clearInterval(progressTimer);
        elements.scanProgress.classList.add('hidden');
    }
}

// Keyboard Shortcuts
function initKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
        // Ctrl/Cmd + K for search focus
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            elements.hashInput?.focus();
        }

        // Escape to close modals/overlays
        if (e.key === 'Escape') {
            hideLoading();
        }
    });
}

// Local Storage Management
function saveToLocalStorage(key, value) {
    try {
        localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
        console.error('Failed to save to localStorage:', error);
    }
}

function loadFromLocalStorage(key, defaultValue = null) {
    try {
        const item = localStorage.getItem(key);
        return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
        console.error('Failed to load from localStorage:', error);
        return defaultValue;
    }
}

// Search History
function saveSearchHistory(query, type) {
    const history = loadFromLocalStorage('searchHistory', []);
    const newEntry = {
        query,
        type,
        timestamp: new Date().toISOString()
    };

    // Remove duplicates and limit to 10 entries
    const filteredHistory = history.filter(item =>
        !(item.query === query && item.type === type)
    );

    filteredHistory.unshift(newEntry);
    saveToLocalStorage('searchHistory', filteredHistory.slice(0, 10));
}

// Enhanced Error Handling
function handleApiError(error, context = '') {
    console.error(`API Error ${context}:`, error);

    if (error.message.includes('401')) {
        showToast('Сессия истекла. Пожалуйста, войдите снова', 'warning');
        logout();
    } else if (error.message.includes('403')) {
        showToast('Недостаточно прав доступа', 'error');
    } else if (error.message.includes('404')) {
        showToast('Данные не найдены', 'warning');
    } else if (error.message.includes('429')) {
        showToast('Слишком много запросов. Попробуйте позже', 'warning');
    } else if (error.message.includes('500')) {
        showToast('Ошибка сервера. Попробуйте позже', 'error');
    } else {
        showToast(`Произошла ошибка: ${error.message}`, 'error');
    }
}

// Update existing functions to use enhanced error handling
const originalMakeRequest = makeRequest;
async function makeRequest(endpoint, options = {}) {
    try {
        return await originalMakeRequest(endpoint, options);
    } catch (error) {
        handleApiError(error, endpoint);
        throw error;
    }
}

// Initialize enhanced features
function initEnhancedFeatures() {
    initKeyboardShortcuts();

    // Add search history to inputs
    if (elements.queryInput) {
        elements.queryInput.addEventListener('input', (e) => {
            debouncedSearch(e.target.value);
        });
    }

    // Replace original upload function
    if (elements.fileInput) {
        elements.fileInput.removeEventListener('change', uploadFile);
        elements.fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                uploadFileEnhanced(e.target.files[0]);
            }
        });
    }

    // Update drag & drop to use enhanced upload
    if (elements.uploadArea) {
        const originalDropHandler = elements.uploadArea.ondrop;
        elements.uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            elements.uploadArea.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                uploadFileEnhanced(files[0]);
            }
        });
    }
}

// Update initialization
const originalInit = init;
function init() {
    originalInit();
    initEnhancedFeatures();
}

// Global functions for HTML onclick handlers
window.scrollToSection = scrollToSection;
